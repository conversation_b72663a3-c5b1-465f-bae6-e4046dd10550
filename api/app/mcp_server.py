"""
MCP Server for OpenMemory with resilient memory client handling.

This module implements an MCP (Model Context Protocol) server that provides
memory operations for OpenMemory. The memory client is initialized lazily
to prevent server crashes when external dependencies (like Ollama) are
unavailable. If the memory client cannot be initialized, the server will
continue running with limited functionality and appropriate error messages.

Key features:
- Lazy memory client initialization
- Graceful error handling for unavailable dependencies
- Fallback to database-only mode when vector store is unavailable
- Proper logging for debugging connection issues
- Environment variable parsing for API keys
"""

import logging
import json
import time
import functools
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from mcp.server.session import ServerSession
from app.utils.memory import get_memory_client
from app.degradation_status import get_system_health_status, format_status_for_display
from app.enhanced_logging import (
    log_memory_operation, operation_logger, MemoryOperationStatus, 
    MemoryOperationResult, create_operation_result, classify_error
)
from fastapi import FastAPI, Request
from fastapi.routing import APIRouter
import contextvars
import os
from dotenv import load_dotenv
from app.database import SessionLocal
from app.models import Memory, MemoryState, MemoryStatusHistory, MemoryAccessLog
from app.utils.db import get_user_and_app
import uuid
import datetime
from app.utils.permissions import check_memory_access_permissions
from qdrant_client import models as qdrant_models
from typing import List, Tuple, Any, Optional

####################################################################################
# CRITICAL FIX: Temporary monkeypatch which avoids crashing when a POST message is received
# before a connection has been initialized, e.g: after a deployment.
# This addresses GitHub issue: https://github.com/modelcontextprotocol/python-sdk/issues/423
# pylint: disable-next=protected-access
old__received_request = ServerSession._received_request

async def _received_request(self, *args, **kwargs):
    try:
        return await old__received_request(self, *args, **kwargs)
    except RuntimeError as e:
        if "Received request before initialization was complete" in str(e):
            logging.warning(f"[MCP_FIX] Handled initialization race condition: {e}")
            return None  # Gracefully handle the race condition
        else:
            raise  # Re-raise other RuntimeErrors

# Apply the monkeypatch
# pylint: disable-next=protected-access
ServerSession._received_request = _received_request
logging.info("[MCP_FIX] Applied monkeypatch to handle MCP initialization race condition")
####################################################################################

# Load environment variables
load_dotenv()

# Initialize MCP with initialization tracking
mcp = FastMCP("mem0-mcp-server")
_initialization_complete = {}  # Track initialization per session

# Don't initialize memory client at import time - do it lazily when needed
def get_memory_client_safe():
    """Get memory client with error handling. Returns None if client cannot be initialized."""
    try:
        return get_memory_client()
    except Exception as e:
        logging.warning(f"Failed to get memory client: {e}")
        return None

# Context variables for user_id and client_name
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar("user_id")
client_name_var: contextvars.ContextVar[str] = contextvars.ContextVar("client_name")

# Create a router for MCP endpoints
mcp_router = APIRouter(prefix="/mcp")

def get_max_text_length_from_config():
    """Get max_text_length from configuration, fallback to default if not available."""
    try:
        from app.database import SessionLocal
        from app.models import Config as ConfigModel
        
        db = SessionLocal()
        try:
            config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
            if config and "openmemory" in config.value and "max_text_length" in config.value["openmemory"]:
                return config.value["openmemory"]["max_text_length"]
        finally:
            db.close()
    except Exception as e:
        logging.warning(f"Error getting max_text_length from config: {e}")
    
    # Fallback to default
    return 2000

def validate_mem0_response(response, operation_type="add_memory"):
    """Validate response from mem0 operations with improved error handling."""
    if not response:
        return False, f"{operation_type} failed: Empty response"
        
    if isinstance(response, dict) and response.get('error'):
        return False, f"{operation_type} failed: {response.get('error')}"
    
    if not isinstance(response, dict):
        return False, f"{operation_type} failed: Invalid response format (expected dict, got {type(response).__name__})"
    
    # Operation-specific validation
    if operation_type in ['add_memory', 'update_memory']:
        return _validate_add_update_response(response, operation_type)
    elif operation_type == 'get_memories':
        return _validate_get_response(response, operation_type)
    elif operation_type == 'search_memories':
        return _validate_search_response(response, operation_type)
    elif operation_type == 'delete_memory':
        return _validate_delete_response(response, operation_type)
    else:
        # Fallback to generic validation for unknown operation types
        return _validate_generic_response(response, operation_type)

def _validate_add_update_response(response, operation_type):
    """Validate add/update memory operation responses."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    if len(results) == 0:
        return False, f"{operation_type} failed: Empty results list"
    
    # Validate each result has required fields
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'id' field"
        
        if 'memory' not in result and 'event' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'memory' or 'event' field"
        
        # Validate memory ID format
        try:
            import uuid
            uuid.UUID(result['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Result {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) processed"

def _validate_get_response(response, operation_type):
    """Validate get memories operation responses."""
    if 'results' not in response:
        # For get operations, empty results might be valid
        return True, f"{operation_type} successful: No memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # Validate each memory in results
    for i, memory in enumerate(results):
        if not isinstance(memory, dict):
            return False, f"{operation_type} failed: Memory {i} is not a dict"
        
        if 'id' not in memory:
            return False, f"{operation_type} failed: Memory {i} missing 'id' field"
        
        # Validate memory ID format
        try:
            import uuid
            uuid.UUID(memory['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Memory {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) retrieved"

def _validate_search_response(response, operation_type):
    """Validate search memories operation responses."""
    if 'results' not in response:
        return True, f"{operation_type} successful: No matching memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # For search, empty results are valid
    if len(results) == 0:
        return True, f"{operation_type} successful: No matching memories found"
    
    # Validate each search result
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Search result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'id' field"
        
        # Search results should have memory content or score
        if 'memory' not in result and 'score' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'memory' or 'score' field"
    
    return True, f"{operation_type} successful: {len(results)} matching memory(ies) found"

def _validate_delete_response(response, operation_type):
    """Validate delete memory operation responses."""
    # Delete operations might have different response formats
    if isinstance(response, dict):
        if 'success' in response:
            return response['success'], f"{operation_type} {'successful' if response['success'] else 'failed'}"
        
        if 'results' in response:
            results = response['results']
            if isinstance(results, list) and len(results) > 0:
                return True, f"{operation_type} successful: {len(results)} memory(ies) deleted"
    
    # If we can't determine success/failure, assume success if no error
    return True, f"{operation_type} completed"

def _validate_generic_response(response, operation_type):
    """Generic validation for unknown operation types."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"

    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"

    return True, f"{operation_type} successful"


def retry_operation(max_attempts=3, backoff_factor=1.5, retry_on_validation_failure=True):
    """
    Decorator to retry operations with exponential backoff.

    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Multiplier for exponential backoff
        retry_on_validation_failure: Whether to retry when validation fails
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            operation_name = getattr(func, '__name__', 'unknown_operation')

            for attempt in range(1, max_attempts + 1):
                try:
                    # Execute the operation
                    result = func(*args, **kwargs)

                    # If operation succeeded, validate the response if requested
                    if retry_on_validation_failure:
                        success, message = validate_mem0_response(result, operation_name)
                        if success:
                            if attempt > 1:
                                logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                            return result
                        else:
                            # Validation failed, treat as retriable error
                            last_exception = Exception(f"Validation failed: {message}")
                            logging.warning(f"Attempt {attempt}/{max_attempts} failed validation for {operation_name}: {message}")
                    else:
                        # No validation, return result directly
                        if attempt > 1:
                            logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                        return result

                except Exception as e:
                    last_exception = e
                    logging.warning(f"Attempt {attempt}/{max_attempts} failed for {operation_name}: {str(e)}")

                # Don't sleep on the last attempt
                if attempt < max_attempts:
                    sleep_time = backoff_factor ** (attempt - 1)
                    logging.info(f"Retrying {operation_name} in {sleep_time:.2f} seconds...")
                    time.sleep(sleep_time)

            # If we get here, all attempts failed
            logging.error(f"Operation {operation_name} failed after {max_attempts} attempts. Last error: {str(last_exception)}")
            raise last_exception
        return wrapper
    return decorator


def retry_memory_operation(max_attempts=3):
    """Specialized retry decorator for memory operations with appropriate defaults."""
    return retry_operation(max_attempts=max_attempts, backoff_factor=1.5, retry_on_validation_failure=True)


class MemoryTransaction:
    """
    Transaction-like behavior for chunked memory operations.
    Ensures atomic behavior and provides rollback capabilities.
    """

    def __init__(self, memory_client, user_id: str, client_name: str):
        self.memory_client = memory_client
        self.user_id = user_id
        self.client_name = client_name
        self.operations = []
        self.results = []
        self.committed = False
        self.transaction_id = str(uuid.uuid4())
        self.start_time = datetime.datetime.now()

        logging.info(f"Created memory transaction {self.transaction_id} for user {user_id}")

    def add_memory_chunk(self, content: str, metadata: dict = None) -> bool:
        """Add a memory chunk to the transaction."""
        if self.committed:
            raise ValueError("Cannot add operations to committed transaction")

        # Add transaction metadata
        chunk_metadata = metadata.copy() if metadata else {}
        chunk_metadata.update({
            'transaction_id': self.transaction_id,
            'chunk_index': len(self.operations),
            'source_app': 'openmemory',
            'mcp_client': self.client_name,
            'is_chunk': True
        })

        # Store operation for later execution
        operation = {
            'type': 'add_memory',
            'content': content,
            'metadata': chunk_metadata,
            'chunk_index': len(self.operations)
        }

        self.operations.append(operation)
        logging.debug(f"Added chunk {len(self.operations)-1} to transaction {self.transaction_id}")
        return True

    def commit(self) -> Tuple[bool, str, List[Any]]:
        """Execute all operations in the transaction."""
        if self.committed:
            raise ValueError("Transaction already committed")

        if not self.operations:
            return True, "No operations to commit", []

        logging.info(f"Committing transaction {self.transaction_id} with {len(self.operations)} operations")

        try:
            # Execute all operations
            success = True
            for i, operation in enumerate(self.operations):
                try:
                    logging.debug(f"Executing operation {i+1}/{len(self.operations)} in transaction {self.transaction_id}")

                    if operation['type'] == 'add_memory':
                        result = add_memory_with_retry(
                            self.memory_client,
                            operation['content'],
                            user_id=self.user_id,
                            metadata=operation['metadata']
                        )

                        # Validate the result
                        success_status, message = validate_mem0_response(result, "add_memory")
                        if not success_status:
                            logging.error(f"Transaction {self.transaction_id} operation {i} failed validation: {message}")
                            success = False
                            break

                        self.results.append(result)
                        logging.debug(f"Transaction {self.transaction_id} operation {i} completed successfully")

                except Exception as e:
                    logging.error(f"Transaction {self.transaction_id} operation {i} failed: {str(e)}")
                    success = False
                    break

            if not success:
                logging.warning(f"Transaction {self.transaction_id} failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Transaction failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            # Verify all chunks are accessible
            if not self._verify_chunks():
                logging.error(f"Transaction {self.transaction_id} chunk verification failed, initiating rollback")
                rollback_success, rollback_message = self.rollback()
                return False, f"Chunk verification failed and rollback {'succeeded' if rollback_success else 'failed'}: {rollback_message}", []

            self.committed = True
            duration = (datetime.datetime.now() - self.start_time).total_seconds()
            logging.info(f"Transaction {self.transaction_id} committed successfully in {duration:.3f}s")

            return True, f"Transaction committed successfully with {len(self.results)} operations", self.results

        except Exception as e:
            logging.error(f"Transaction {self.transaction_id} commit failed with exception: {str(e)}")
            rollback_success, rollback_message = self.rollback()
            return False, f"Transaction failed with exception and rollback {'succeeded' if rollback_success else 'failed'}: {str(e)}", []

    def rollback(self) -> Tuple[bool, str]:
        """Roll back the transaction by removing any stored chunks."""
        if not self.results:
            return True, "No operations to rollback"

        logging.warning(f"Rolling back transaction {self.transaction_id} with {len(self.results)} operations")

        rollback_errors = []
        successful_rollbacks = 0

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']
                            # Mark memory as deleted in database
                            try:
                                db = SessionLocal()
                                try:
                                    memory = db.query(Memory).filter(Memory.id == uuid.UUID(memory_id)).first()
                                    if memory:
                                        memory.state = MemoryState.deleted.value
                                        memory.deleted_at = datetime.datetime.now(datetime.UTC)

                                        # Create history entry
                                        history = MemoryStatusHistory(
                                            memory_id=uuid.UUID(memory_id),
                                            changed_by=None,  # System rollback
                                            old_state=MemoryState.active.value,
                                            new_state=MemoryState.deleted.value
                                        )
                                        db.add(history)
                                        db.commit()
                                        successful_rollbacks += 1
                                        logging.debug(f"Rolled back memory {memory_id} in transaction {self.transaction_id}")
                                finally:
                                    db.close()
                            except Exception as db_error:
                                rollback_errors.append(f"Database rollback failed for {memory_id}: {str(db_error)}")

            except Exception as e:
                rollback_errors.append(f"Rollback operation {i} failed: {str(e)}")

        self.results = []

        if rollback_errors:
            error_message = f"Rollback partially failed: {len(rollback_errors)} errors, {successful_rollbacks} successful"
            logging.error(f"Transaction {self.transaction_id} rollback errors: {rollback_errors}")
            return False, error_message
        else:
            success_message = f"Rollback successful: {successful_rollbacks} operations rolled back"
            logging.info(f"Transaction {self.transaction_id} rollback completed: {success_message}")
            return True, success_message

    def _verify_chunks(self) -> bool:
        """Verify all chunks are accessible after commit."""
        if not self.results:
            return True

        logging.debug(f"Verifying {len(self.results)} chunks in transaction {self.transaction_id}")

        for i, result in enumerate(self.results):
            try:
                if isinstance(result, dict) and 'results' in result:
                    for memory_result in result['results']:
                        if 'id' in memory_result:
                            memory_id = memory_result['id']

                            # Verify memory exists in database
                            db = SessionLocal()
                            try:
                                memory = db.query(Memory).filter(
                                    Memory.id == uuid.UUID(memory_id),
                                    Memory.state == MemoryState.active.value
                                ).first()

                                if not memory:
                                    logging.error(f"Chunk verification failed: memory {memory_id} not found in database")
                                    return False

                            finally:
                                db.close()

                            # Verify memory is accessible via vector store
                            try:
                                search_result = search_memories_with_retry(
                                    self.memory_client,
                                    memory_result.get('memory', '')[:50],  # Search with first 50 chars
                                    user_id=self.user_id,
                                    limit=1
                                )

                                # Check if our memory is in the search results
                                found = False
                                if search_result:
                                    for search_memory in search_result:
                                        if search_memory.get('id') == memory_id:
                                            found = True
                                            break

                                if not found:
                                    logging.warning(f"Chunk verification: memory {memory_id} not immediately searchable (may be indexing)")
                                    # Don't fail verification for search issues as indexing may be delayed

                            except Exception as search_error:
                                logging.warning(f"Chunk verification search failed for {memory_id}: {str(search_error)}")
                                # Don't fail verification for search issues

            except Exception as e:
                logging.error(f"Chunk verification failed for result {i}: {str(e)}")
                return False

        logging.debug(f"All chunks verified successfully for transaction {self.transaction_id}")
        return True

    def get_status(self) -> dict:
        """Get transaction status information."""
        return {
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'client_name': self.client_name,
            'operations_count': len(self.operations),
            'results_count': len(self.results),
            'committed': self.committed,
            'start_time': self.start_time.isoformat(),
            'duration_seconds': (datetime.datetime.now() - self.start_time).total_seconds()
        }


def verify_memory_storage(response, memory_client, request_id=None):
    """Verify that memories were actually stored by attempting to retrieve them."""
    import time
    
    if not isinstance(response, dict) or 'results' not in response:
        return False, "Invalid response format for verification"
    
    results = response['results']
    if not isinstance(results, list) or len(results) == 0:
        return False, "No results to verify"
    
    stored_memory_ids = []
    for result in results:
        if isinstance(result, dict) and 'id' in result:
            stored_memory_ids.append(result['id'])
    
    if not stored_memory_ids:
        return False, "No memory IDs found in response for verification"
    
    # Wait a brief moment for eventual consistency
    time.sleep(0.1)
    
    # Attempt to retrieve stored memories to verify they exist
    try:
        verification_start = time.time()
        
        # Try to get all memories to see if our stored memories are present
        all_memories = memory_client.get_all()
        
        verification_duration = time.time() - verification_start
        
        if request_id:
            logging.info(f"[REQ_{request_id}] Memory verification completed in {verification_duration:.3f}s")
        
        if not isinstance(all_memories, dict) or 'results' not in all_memories:
            return False, "Unable to retrieve memories for verification"
        
        existing_memory_ids = set()
        for memory in all_memories['results']:
            if isinstance(memory, dict) and 'id' in memory:
                existing_memory_ids.add(memory['id'])
        
        # Check if all stored memories are present
        missing_memories = []
        for memory_id in stored_memory_ids:
            if memory_id not in existing_memory_ids:
                missing_memories.append(memory_id)
        
        if missing_memories:
            return False, f"Memory verification failed: {len(missing_memories)} memories not found after storage"
        
        if request_id:
            logging.info(f"[REQ_{request_id}] Memory verification successful: {len(stored_memory_ids)} memories confirmed stored")
        
        return True, f"Memory verification successful: {len(stored_memory_ids)} memories confirmed stored"
        
    except Exception as e:
        error_msg = f"Memory verification failed due to error: {str(e)}"
        if request_id:
            logging.error(f"[REQ_{request_id}] {error_msg}")
        return False, error_msg

def validate_text_length(text: str) -> tuple[bool, str]:
    """Validate text length for memory storage."""
    max_length = get_max_text_length_from_config()
    if len(text) > max_length:
        return False, f"Warning: Text is {len(text)} characters (over {max_length} limit). Will be automatically chunked for processing."
    return True, "Text length valid"

def chunk_text(text: str, max_length: int = None) -> list[str]:
    """
    Chunk text into smaller pieces for reliable memory storage.
    Implements smart chunking that preserves sentence boundaries when possible.
    """
    if max_length is None:
        max_length = get_max_text_length_from_config()
    
    # If text is already short enough, return as-is
    if len(text) <= max_length:
        return [text]
    
    logging.info(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, chunking...")
    
    chunks = []
    current_pos = 0
    
    while current_pos < len(text):
        # Calculate chunk end position
        chunk_end = min(current_pos + max_length, len(text))
        
        # If this is not the last chunk, try to break at sentence boundary
        if chunk_end < len(text):
            # Look for sentence endings within the last 200 chars of the chunk
            search_start = max(current_pos, chunk_end - 200)
            sentence_endings = []
            
            for i in range(search_start, chunk_end):
                if text[i] in '.!?':
                    # Check if this is likely a sentence ending (not abbreviation)
                    if i + 1 < len(text) and text[i + 1] in ' \n\t':
                        sentence_endings.append(i + 1)
            
            # Use the last sentence ending found, or fall back to word boundary
            if sentence_endings:
                chunk_end = sentence_endings[-1]
            else:
                # Look for word boundary within last 100 chars
                search_start = max(current_pos, chunk_end - 100)
                word_boundaries = []
                
                for i in range(search_start, chunk_end):
                    if text[i] in ' \n\t':
                        word_boundaries.append(i)
                
                if word_boundaries:
                    chunk_end = word_boundaries[-1]
        
        # Extract the chunk
        chunk = text[current_pos:chunk_end].strip()
        if chunk:  # Only add non-empty chunks
            chunks.append(chunk)
            logging.info(f"CHUNKING: Created chunk {len(chunks)} with length {len(chunk)}")
        
        current_pos = chunk_end
    
    logging.info(f"CHUNKING: Split {len(text)} chars into {len(chunks)} chunks")
    return chunks

async def _add_single_memory(uid: str, client_name: str, text: str, parent_request_id: int = None) -> str:
    """
    Add a single memory chunk to the system.
    This is used internally for processing individual chunks.
    """
    chunk_start_time = datetime.datetime.now()
    req_prefix = f"[REQ_{parent_request_id}_CHUNK]" if parent_request_id else "[CHUNK]"
    
    logging.info(f"{req_prefix} CHUNKING: Processing single memory chunk of {len(text)} chars")
    
    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable."
    
    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            
            # Check if app is active
            if not app.is_active:
                return f"Error: App {app.name} is currently paused on OpenMemory."
            
            # CLAUDE DESKTOP FIX: Use shared vector store context for chunking too
            mem0_user_id = uid  # Use shared context to solve Claude Desktop isolation issue
            logging.info(f"CHUNKING: Calling mem0 for chunk of {len(text)} chars using shared context '{mem0_user_id}'")
            
            response = add_memory_with_retry(
                memory_client,
                text,
                user_id=mem0_user_id,
                metadata={
                    "source_app": "openmemory",
                    "mcp_client": client_name,
                }
            )
            
            # Validate mem0 response
            response_valid, response_message = validate_mem0_response(response, "add_memory")
            if not response_valid:
                logging.error(f"CHUNKING: Memory storage failed for chunk: {response_message}")
                return f"Error: {response_message}"
            
            # Verify memory existence after storage
            verification_success, verification_message = verify_memory_storage(response, memory_client)
            if not verification_success:
                logging.error(f"CHUNKING: Memory verification failed for chunk: {verification_message}")
                return f"Error: Memory storage verification failed - {verification_message}"
            
            # Process the response and update database
            if isinstance(response, dict) and 'results' in response:
                for result in response['results']:
                    memory_id = uuid.UUID(result['id'])
                    memory = db.query(Memory).filter(Memory.id == memory_id).first()
                    
                    if result['event'] == 'ADD':
                        if not memory:
                            memory = Memory(
                                id=memory_id,
                                user_id=user.id,
                                app_id=app.id,
                                content=result['memory'],
                                state=MemoryState.active.value
                            )
                            db.add(memory)
                        else:
                            memory.state = MemoryState.active.value
                            memory.content = result['memory']
                        
                        # Create history entry
                        history = MemoryStatusHistory(
                            memory_id=memory_id,
                            changed_by=user.id,
                            old_state=MemoryState.deleted.value if memory else None,
                            new_state=MemoryState.active.value
                        )
                        db.add(history)
                
                db.commit()
                logging.info(f"CHUNKING: Successfully processed chunk")
                return "Successfully processed chunk"
        finally:
            db.close()
    except Exception as e:
        logging.error(f"CHUNKING: Error processing chunk: {e}")
        return f"Error processing chunk: {e}"

# Retry-enabled memory operations with enhanced logging
@log_memory_operation("add_memory")
@retry_memory_operation(max_attempts=3)
def add_memory_with_retry(memory_client, text, user_id, metadata):
    """Add memory with retry logic and validation."""
    return memory_client.add(text, user_id=user_id, metadata=metadata)


@log_memory_operation("get_memories")
@retry_memory_operation(max_attempts=3)
def get_all_memories_with_retry(memory_client, user_id):
    """Get all memories with retry logic."""
    return memory_client.get_all(user_id=user_id)


@log_memory_operation("search_memories")
@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def search_memories_with_retry(memory_client, query, user_id, limit=10):
    """Search memories with retry logic (no validation since search has different response format)."""
    return memory_client.search(query, user_id=user_id, limit=limit)


@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def query_vector_store_with_retry(vector_store_client, collection_name, query, query_filter, limit):
    """Query vector store with retry logic."""
    return vector_store_client.query_points(
        collection_name=collection_name,
        query=query,
        query_filter=query_filter,
        limit=limit
    )


# Initialize SSE transport
sse = SseServerTransport("/mcp/messages/")

# Global tracking for concurrent requests
_active_requests = {}
_request_counter = 0

@mcp.tool(description="Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevant information which can be useful in the future conversation. This can also be called when the user asks you to remember something.")
async def add_memories(text: str) -> str:
    global _request_counter
    _request_counter += 1
    request_id = _request_counter
    start_time = datetime.datetime.now()
    
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    session_key = f"{uid}:{client_name}"

    # ENHANCED DIAGNOSTIC: Track concurrent requests and context state
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: add_memories called with uid={uid}, client_name={client_name}, text_length={len(text)} chars")
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Active concurrent requests: {len(_active_requests)}")
    logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Context state - uid_var_id: {id(user_id_var)}, client_var_id: {id(client_name_var)}")
    
    # Track this request
    _active_requests[request_id] = {
        'uid': uid,
        'client_name': client_name,
        'start_time': start_time,
        'text_length': len(text)
    }
    
    # Write enhanced debugging to file
    with open("/tmp/debug_add_memories.log", "a") as f:
        f.write(f"[REQ_{request_id}] {start_time.isoformat()} - add_memories called: uid={uid}, client_name={client_name}, text_length={len(text)}, concurrent_count={len(_active_requests)}\n")

    if not uid:
        logging.error("Error: user_id not provided in context variables")
        return "Error: user_id not provided"
    if not client_name:
        logging.error("Error: client_name not provided in context variables")
        return "Error: client_name not provided"
    
    # Check text length and determine if chunking is needed
    max_length = get_max_text_length_from_config()
    needs_chunking = len(text) > max_length
    
    if needs_chunking:
        logging.warning(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, will auto-chunk")
        logging.info(f"DIAGNOSTIC: Text length check - Input: {len(text)} chars, Max allowed: {max_length} chars")
        
        # Chunk the text
        chunks = chunk_text(text, max_length)
        logging.info(f"CHUNKING: Created {len(chunks)} chunks from original text")

        # Get memory client for transaction
        memory_client = get_memory_client_safe()
        if not memory_client:
            if request_id in _active_requests:
                del _active_requests[request_id]
            return "Error: Memory system is currently unavailable for chunked operation. Please try again later."

        # Use transaction for atomic chunked operation
        transaction = MemoryTransaction(memory_client, uid, client_name)

        # Add all chunks to transaction
        for i, chunk in enumerate(chunks):
            chunk_metadata = {
                "source_app": "openmemory",
                "mcp_client": client_name,
                "chunk_part": f"{i+1}/{len(chunks)}",
                "original_length": len(text),
                "chunk_length": len(chunk)
            }

            chunk_content = f"[Part {i+1}/{len(chunks)}] {chunk}"
            success = transaction.add_memory_chunk(chunk_content, chunk_metadata)

            if not success:
                logging.error(f"[REQ_{request_id}] CHUNKING: Failed to add chunk {i+1} to transaction")
                if request_id in _active_requests:
                    del _active_requests[request_id]
                return f"Error: Failed to prepare chunk {i+1}/{len(chunks)} for processing"

        logging.info(f"[REQ_{request_id}] CHUNKING: Added {len(chunks)} chunks to transaction {transaction.transaction_id}")

        # Commit the transaction
        commit_success, commit_message, results = transaction.commit()

        total_duration = (datetime.datetime.now() - start_time).total_seconds()

        if commit_success:
            logging.info(f"[REQ_{request_id}] CHUNKING: Transaction committed successfully in {total_duration:.3f}s")
            logging.info(f"[REQ_{request_id}] CHUNKING: Processed {len(results)} chunks atomically")

            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]

            return f"Successfully chunked and stored {len(results)} memory pieces from {len(text)} character text via app '{client_name}' (Transaction: {transaction.transaction_id})"
        else:
            logging.error(f"[REQ_{request_id}] CHUNKING: Transaction failed: {commit_message}")
            logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Active concurrent requests during chunk failure: {len(_active_requests)}")

            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]

            return f"Error: Chunked operation failed - {commit_message}"
    else:
        logging.info(f"DIAGNOSTIC: Text length {len(text)} is within limit {max_length}, processing normally")
    
    # Check if initialization is complete for this session
    if session_key not in _initialization_complete or not _initialization_complete[session_key]:
        logging.warning(f"[REQ_{request_id}] [MCP_FIX] Handled initialization race condition: Received request before initialization was complete")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Session {session_key} not fully initialized. Status: {_initialization_complete.get(session_key, 'NOT_FOUND')}")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Total active sessions: {len(_initialization_complete)}")
        logging.info(f"[REQ_{request_id}] DIAGNOSTIC: All session keys: {list(_initialization_complete.keys())}")
        
        # Clean up request tracking
        if request_id in _active_requests:
            del _active_requests[request_id]
            
        # For memory operations, we need to ensure proper initialization
        # Return early with a clear error message instead of proceeding with uninitialized state
        return "Error: Memory system is initializing. Please wait a moment and try again."

    # Get memory client safely
    logging.info(f"[MCP_DIAGNOSTIC] Attempting to get memory client for session {session_key}")
    memory_client = get_memory_client_safe()
    if not memory_client:
        logging.error(f"[MCP_DIAGNOSTIC] Memory client unavailable for session {session_key}")
        return "Error: Memory system is currently unavailable. Please try again later."
    logging.info(f"[MCP_DIAGNOSTIC] Memory client successfully obtained for session {session_key}")

    try:
        db = SessionLocal()
        try:
            # Get or create user and app with enhanced logging
            logging.info(f"Getting or creating user '{uid}' and app '{client_name}'")
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"User ID: {user.id}, App ID: {app.id}, App Name: {app.name}, App Active: {app.is_active}")
            
            # DIAGNOSTIC: Check user's total memory count across all apps
            total_user_memories = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.state == MemoryState.active.value
            ).count()
            app_specific_memories = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == MemoryState.active.value
            ).count()
            logging.info(f"DIAGNOSTIC: User '{uid}' has {total_user_memories} total memories, {app_specific_memories} for app '{client_name}'")

            # Check if app is active
            if not app.is_active:
                logging.warning(f"App {app.name} is paused")
                return f"Error: App {app.name} is currently paused on OpenMemory. Cannot create new memories."

            logging.info(f"Adding memory for user_id='{uid}'")
            
            # Get initial memory count for validation
            initial_memory_count = db.query(Memory).filter(
                Memory.user_id == user.id,
                Memory.app_id == app.id,
                Memory.state == MemoryState.active.value
            ).count()
            logging.info(f"Initial active memory count: {initial_memory_count}")
            
            logging.info(f"DIAGNOSTIC: About to call memory_client.add with text length: {len(text)} chars")
            logging.info(f"DIAGNOSTIC: mem0 call parameters - user_id='{uid}', text='{text[:100]}...', metadata={{'source_app': 'openmemory', 'mcp_client': '{client_name}'}}")
            
            # DIAGNOSTIC: Check if this is likely a mem0 LLM classification issue
            if app_specific_memories == 0:
                logging.warning(f"DIAGNOSTIC: No existing memories for app '{client_name}' - mem0 LLM may classify content as non-memorable")
                logging.info(f"DIAGNOSTIC: Total user memories across all apps: {total_user_memories}")
                if total_user_memories > 0:
                    logging.info(f"DIAGNOSTIC: SOLUTION - Using shared user context to leverage existing {total_user_memories} memories")
            
            # CLAUDE DESKTOP FIX: Use shared vector store context to solve isolation issue
            # Force all clients to use the same mem0 context to leverage existing memories
            mem0_user_id = uid  # Remove client-specific isolation - use shared context
            logging.info(f"DIAGNOSTIC: Using shared vector store context '{mem0_user_id}' (DB memories: {total_user_memories}, client: {client_name})")
            
            # ENHANCED DIAGNOSTIC: Pre-request state check
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: About to call mem0 - checking vector store state")
            try:
                # Check if mem0 can see existing memories
                search_result = memory_client.search(query="test", user_id=mem0_user_id, limit=1)
                existing_memories_count = len(search_result) if search_result else 0
                logging.info(f"[REQ_{request_id}] DIAGNOSTIC: mem0 vector store shows {existing_memories_count} existing memories for user '{mem0_user_id}'")
            except Exception as search_error:
                logging.warning(f"[REQ_{request_id}] DIAGNOSTIC: Could not check existing memories: {search_error}")
            
            # Call mem0 with enhanced timing and graceful degradation
            mem0_start_time = datetime.datetime.now()
            response = memory_client.add_memory_with_degradation(
                text,
                metadata={
                    "source_app": "openmemory",
                    "mcp_client": client_name,
                    "user_id": mem0_user_id
                }
            )
            mem0_duration = (datetime.datetime.now() - mem0_start_time).total_seconds()
            
            # Check if operation was performed in degraded mode
            is_degraded = any(item.get('fallback_mode', False) for item in response.get('results', []))
            if is_degraded:
                logging.warning(f"[REQ_{request_id}] Memory stored in degraded mode (vector store unavailable)")
                print(f"DEGRADED: Memory stored in database-only mode for request {request_id}")

            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: mem0.add() completed in {mem0_duration:.3f}s")
            logging.info(f"[REQ_{request_id}] Qdrant response: {response}")
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Response received, type: {type(response)}, has_results: {'results' in response if isinstance(response, dict) else 'N/A'}")

            # Enhanced response validation
            response_valid, response_message = validate_mem0_response(response, "add_memory")
            if not response_valid:
                logging.error(f"[REQ_{request_id}] Memory storage validation failed: {response_message}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Full response from mem0: {response}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Response type: {type(response)}")
                logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Request duration so far: {(datetime.datetime.now() - start_time).total_seconds():.3f}s")
                
                # Clean up request tracking
                if request_id in active_requests:
                    del active_requests[request_id]
                return {"error": response_message}
            
            # Verify memory existence after storage
            verification_success, verification_message = verify_memory_storage(response, memory_client, request_id)
            if not verification_success:
                logging.error(f"[REQ_{request_id}] Memory verification failed: {verification_message}")
                # Clean up request tracking
                if request_id in active_requests:
                    del active_requests[request_id]
                return {"error": f"Memory storage verification failed: {verification_message}"}

            # Process the response and update database
            if isinstance(response, dict) and 'results' in response:
                logging.info(f"Processing {len(response['results'])} results from Qdrant")
                for result in response['results']:
                    memory_id = uuid.UUID(result['id'])
                    memory = db.query(Memory).filter(Memory.id == memory_id).first()
                    logging.info(f"Processing result: {result['event']} for memory_id={memory_id}")

                    if result['event'] == 'ADD':
                        if not memory:
                            memory = Memory(
                                id=memory_id,
                                user_id=user.id,
                                app_id=app.id,
                                content=result['memory'],
                                state=MemoryState.active.value
                            )
                            db.add(memory)
                            logging.info(f"Created new memory in database: {memory_id}")
                        else:
                            memory.state = MemoryState.active.value
                            memory.content = result['memory']
                            logging.info(f"Updated existing memory in database: {memory_id}")

                        # Create history entry
                        history = MemoryStatusHistory(
                            memory_id=memory_id,
                            changed_by=user.id,
                            old_state=MemoryState.deleted.value if memory else None,
                            new_state=MemoryState.active.value
                        )
                        db.add(history)

                    elif result['event'] == 'DELETE':
                        if memory:
                            memory.state = MemoryState.deleted.value
                            memory.deleted_at = datetime.datetime.now(datetime.UTC)
                            logging.info(f"Marked memory as deleted: {memory_id}")
                            # Create history entry
                            history = MemoryStatusHistory(
                                memory_id=memory_id,
                                changed_by=user.id,
                                old_state=MemoryState.active.value,
                                new_state=MemoryState.deleted.value
                            )
                            db.add(history)

                logging.info("Committing database changes")
                db.commit()
                logging.info("Database commit successful")
                
                # Post-commit validation: Verify memories were actually stored
                final_memory_count = db.query(Memory).filter(
                    Memory.user_id == user.id,
                    Memory.app_id == app.id,
                    Memory.state == MemoryState.active.value
                ).count()
                logging.info(f"Final active memory count: {final_memory_count}")
                
                if final_memory_count <= initial_memory_count:
                    logging.error(f"Memory storage verification failed: count unchanged ({initial_memory_count} -> {final_memory_count})")
                    return "Error: Memory appears to have been processed but was not stored. Please try with shorter text chunks."
                
                memories_added = final_memory_count - initial_memory_count
                logging.info(f"Successfully stored {memories_added} new memory/memories")

            total_duration = (datetime.datetime.now() - start_time).total_seconds()
            logging.info(f"[REQ_{request_id}] DIAGNOSTIC: Request completed successfully in {total_duration:.3f}s")
            
            # Clean up request tracking
            if request_id in _active_requests:
                del _active_requests[request_id]
            
            return f"Successfully added {memories_added if 'memories_added' in locals() else 'memory'} for user '{uid}' via app '{client_name}'"
        finally:
            db.close()
    except Exception as e:
        total_duration = (datetime.datetime.now() - start_time).total_seconds()
        logging.exception(f"[REQ_{request_id}] ERROR: Exception after {total_duration:.3f}s: {e}")
        logging.error(f"[REQ_{request_id}] DIAGNOSTIC: Context at error - uid={uid}, client_name={client_name}, active_requests={len(_active_requests)}")
        
        # Clean up request tracking
        if request_id in _active_requests:
            del _active_requests[request_id]
            
        return f"Error adding to memory: {e}"


@mcp.tool(description="Search through stored memories. This method is called EVERYTIME the user asks anything.")
async def search_memory(query: str) -> str:
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"

    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get accessible memory IDs based on ACL
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]
            
            # Use graceful degradation for search
            search_response = memory_client.search_memory_with_degradation(query, limit=10)
            
            # Check if search was performed in degraded mode
            if search_response.get('degraded_mode', False):
                logging.warning(f"Search performed in degraded mode for user {uid}")
                print(f"DEGRADED: Search operation with limited functionality for user {uid}")
                return json.dumps({
                    "results": [],
                    "degraded_mode": True,
                    "message": "Vector store unavailable - search functionality limited"
                }, indent=2)
            
            # Process normal search results
            memories = search_response.get('results', [])
            
            # Filter memories based on ACL if we have accessible memory IDs
            if accessible_memory_ids:
                accessible_memory_ids_str = [str(memory_id) for memory_id in accessible_memory_ids]
                memories = [memory for memory in memories if memory.get('id') in accessible_memory_ids_str]
            
            # Format memories for consistent output
            formatted_memories = []
            for memory in memories:
                formatted_memory = {
                    "id": memory.get('id'),
                    "memory": memory.get('memory'),
                    "hash": memory.get('hash'),
                    "created_at": memory.get('created_at'),
                    "updated_at": memory.get('updated_at'),
                    "score": memory.get('score', 0.0),
                }
                formatted_memories.append(formatted_memory)
            
            memories = formatted_memories

            # Log memory access for each memory found
            if isinstance(memories, dict) and 'results' in memories:
                print(f"Memories: {memories}")
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="search",
                            metadata_={
                                "query": query,
                                "score": memory_data.get('score'),
                                "hash": memory_data.get('hash')
                            }
                        )
                        db.add(access_log)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    # Create access log entry
                    access_log = MemoryAccessLog(
                        memory_id=memory_id,
                        app_id=app.id,
                        access_type="search",
                        metadata_={
                            "query": query,
                            "score": memory.get('score'),
                            "hash": memory.get('hash')
                        }
                    )
                    db.add(access_log)
                db.commit()
            return json.dumps(memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(e)
        return f"Error searching memory: {e}"


@mcp.tool(description="List all memories in the user's memory")
async def list_memories() -> str:
    uid = user_id_var.get(None)
    client_name = client_name_var.get(None)
    if not uid:
        return "Error: user_id not provided"
    if not client_name:
        return "Error: client_name not provided"

    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get all memories with retry logic
            memories = get_all_memories_with_retry(memory_client, user_id=uid)
            filtered_memories = []

            # Filter memories based on permissions
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]
            if isinstance(memories, dict) and 'results' in memories:
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        if memory_id in accessible_memory_ids:
                            # Create access log entry
                            access_log = MemoryAccessLog(
                                memory_id=memory_id,
                                app_id=app.id,
                                access_type="list",
                                metadata_={
                                    "hash": memory_data.get('hash')
                                }
                            )
                            db.add(access_log)
                            filtered_memories.append(memory_data)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    memory_obj = db.query(Memory).filter(Memory.id == memory_id).first()
                    if memory_obj and check_memory_access_permissions(db, memory_obj, app.id):
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="list",
                            metadata_={
                                "hash": memory.get('hash')
                            }
                        )
                        db.add(access_log)
                        filtered_memories.append(memory)
                db.commit()
            return json.dumps(filtered_memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(f"Error getting memories: {e}")
        return f"Error getting memories: {e}"


@mcp.tool(description="Get system health status and degradation information for the memory system")
async def get_system_health() -> str:
    """Get comprehensive system health status including degradation information."""
    try:
        # Get system health status
        health_status = get_system_health_status()
        
        # Format for display
        formatted_status = format_status_for_display(health_status)
        
        # Also return JSON for programmatic access
        json_status = json.dumps(health_status, indent=2)
        
        return f"{formatted_status}\n\n--- Raw JSON Status ---\n{json_status}"
        
    except Exception as e:
        logging.exception(f"Error getting system health status: {e}")
        return f"Error getting system health status: {e}"


@mcp.tool(description="Get detailed operation metrics and performance statistics for memory operations")
async def get_operation_metrics() -> str:
    """Get comprehensive operation metrics including timing, success rates, and error analysis."""
    try:
        # Get operation metrics from the enhanced logger
        metrics = operation_logger.get_operation_metrics()
        active_operations = operation_logger.get_active_operations()
        
        # Format metrics for display
        formatted_output = ["=== Memory Operation Metrics ==="]
        formatted_output.append(f"Metrics collected at: {datetime.datetime.fromtimestamp(metrics['metrics_timestamp']).isoformat()}")
        formatted_output.append(f"Active operations: {metrics['active_operations_count']}")
        
        if active_operations:
            formatted_output.append("\n--- Currently Active Operations ---")
            for op_id, op_info in active_operations.items():
                formatted_output.append(f"  {op_id[:8]}: {op_info['operation_type']} ({op_info['duration_ms']}ms)")
                if op_info.get('is_long_running'):
                    formatted_output.append(f"    ⚠️  Long-running operation detected")
        
        if metrics['operations']:
            formatted_output.append("\n--- Operation Statistics ---")
            for op_type, stats in metrics['operations'].items():
                success_rate = (stats['success_count'] / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
                formatted_output.append(f"\n{op_type.upper()}:")
                formatted_output.append(f"  Total operations: {stats['total_count']}")
                formatted_output.append(f"  Success rate: {success_rate:.1f}% ({stats['success_count']}/{stats['total_count']})")
                formatted_output.append(f"  Failures: {stats['failure_count']}")
                formatted_output.append(f"  Total retries: {stats['retry_count']}")
                
                if stats['avg_duration_ms'] > 0:
                    formatted_output.append(f"  Average duration: {stats['avg_duration_ms']:.1f}ms")
                    formatted_output.append(f"  Duration range: {stats['min_duration_ms']}-{stats['max_duration_ms']}ms")
                
                # Performance distribution
                perf_dist = stats['performance_distribution']
                total_perf = sum(perf_dist.values())
                if total_perf > 0:
                    formatted_output.append(f"  Performance distribution:")
                    for perf_level, count in perf_dist.items():
                        percentage = (count / total_perf * 100) if total_perf > 0 else 0
                        formatted_output.append(f"    {perf_level}: {count} ({percentage:.1f}%)")
                
                # Error breakdown
                if stats['error_types']:
                    formatted_output.append(f"  Error breakdown:")
                    for error_type, count in stats['error_types'].items():
                        error_percentage = (count / stats['failure_count'] * 100) if stats['failure_count'] > 0 else 0
                        formatted_output.append(f"    {error_type}: {count} ({error_percentage:.1f}% of failures)")
                
                if stats['last_operation']:
                    last_op_time = datetime.datetime.fromtimestamp(stats['last_operation'])
                    formatted_output.append(f"  Last operation: {last_op_time.isoformat()}")
        else:
            formatted_output.append("\nNo operation statistics available yet.")
        
        # Add raw JSON for programmatic access
        formatted_output.append("\n\n--- Raw JSON Metrics ---")
        formatted_output.append(json.dumps({
            "metrics": metrics,
            "active_operations": active_operations
        }, indent=2))
        
        return "\n".join(formatted_output)
        
    except Exception as e:
        logging.exception(f"Error getting operation metrics: {e}")
        return f"Error getting operation metrics: {e}"


# SECURITY: delete_all_memories tool has been REMOVED for safety
# This dangerous operation is now only available through the secure UI with password protection
# MCP clients should not have the ability to bulk delete all memories


@mcp_router.get("/claude/sse/{user_id}")
async def handle_claude_sse(request: Request):
    """Handle SSE connections for Claude Desktop specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "claude"  # Fixed client name for Claude Desktop
    session_key = f"{uid}:{client_name}"
    
    logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop SSE connection initiated for user: {uid}, session: {session_key}")
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"[MCP_DIAGNOSTIC] Pre-registered user '{uid}' and app '{client_name}' for Claude Desktop SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"[MCP_DIAGNOSTIC] Failed to pre-register user/app for Claude Desktop: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with proper initialization sequence for Claude Desktop
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            logging.info(f"[MCP_DIAGNOSTIC] Starting Claude Desktop initialization for session {session_key}")
            
            # Create a custom initialization handler
            class ClaudeInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Shorter wait for Claude Desktop
                        import asyncio
                        await asyncio.sleep(0.3)
                        
                        # Mark as initialized before starting
                        _initialization_complete[session_key] = True
                        logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop initialization complete for session {session_key}")
                        
                        # Run the actual server
                        await self._server.run(read_stream, write_stream, init_options)
                    except Exception as e:
                        logging.error(f"[MCP_DIAGNOSTIC] Claude Desktop server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = ClaudeInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"[MCP_DIAGNOSTIC] Claude Desktop SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)
        logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop session cleanup complete for {session_key}")


@mcp_router.get("/{client_name}/sse/{user_id}")
async def handle_sse(request: Request):
    """Handle SSE connections for a specific user and client with improved initialization"""
    # Extract user_id and client_name from path parameters
    uid = request.path_params.get("user_id")
    client_name = request.path_params.get("client_name")
    session_key = f"{uid}:{client_name}"
    
    logging.info(f"[MCP_DIAGNOSTIC] Generic SSE connection initiated for client: {client_name}, user: {uid}, session: {session_key}")
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name or "")

    try:
        # Handle SSE connection with proper initialization sequence
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler
            class InitializationAwareServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Wait a bit to ensure proper initialization sequence
                        import asyncio
                        await asyncio.sleep(0.2)
                        
                        # CRITICAL FIX: Do NOT mark as initialized until AFTER server.run() is ready
                        # This fixes the race condition with roo-cline requests
                        logging.info(f"Starting server initialization for session {session_key}")
                        
                        # Create a custom server wrapper that delays initialization marking
                        class DelayedInitServer:
                            def __init__(self, server, session_key):
                                self._server = server
                                self._session_key = session_key
                                
                            async def run(self, read_stream, write_stream, init_options):
                                # First establish the connection
                                await self._server.run(read_stream, write_stream, init_options)
                        
                        # Use delayed init server instead of direct run
                        delayed_server = DelayedInitServer(self._server, session_key)
                        
                        # Mark as initialized ONLY after we're about to start handling requests
                        _initialization_complete[session_key] = True
                        logging.info(f"Initialization complete for session {session_key}")
                        
                        await delayed_server.run(read_stream, write_stream, init_options)
                        
                    except Exception as e:
                        logging.error(f"Server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = InitializationAwareServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.get("/roocline/sse/{user_id}")
async def handle_roocline_sse(request: Request):
    """Handle SSE connections for roo-cline specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "roo-cline"  # Fixed client name for roo-cline
    session_key = f"{uid}:{client_name}"
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for roo-cline SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app for roo-cline: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with longer initialization delay for roo-cline
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler with longer delay
            class RoolineInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # CRITICAL FIX for roo-cline race condition
                        import asyncio
                        await asyncio.sleep(0.5)
                        
                        logging.info(f"Starting roo-cline server initialization for session {session_key}")
                        
                        # FIXED: Mark as initialized AFTER server is ready to handle requests
                        # This prevents the "Received request before initialization was complete" error
                        
                        # Create a task to run the server
                        async def run_server():
                            await self._server.run(read_stream, write_stream, init_options)
                        
                        # Mark as initialized just before we start the server task
                        _initialization_complete[session_key] = True
                        logging.info(f"Roo-cline initialization complete for session {session_key}")
                        
                        # Now run the server
                        await run_server()
                        
                    except Exception as e:
                        logging.error(f"Roo-cline server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = RoolineInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"Roo-cline SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.get("/cline/sse/{user_id}")
async def handle_cline_sse(request: Request):
    """Handle SSE connections for cline specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "cline"  # Fixed client name for cline
    session_key = f"{uid}:{client_name}"
    
    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for cline SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app for cline: {e}")
    
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with longer initialization delay for cline
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            
            # Create a custom initialization handler with longer delay
            class ClineInitializationServer:
                def __init__(self, server):
                    self._server = server
                    
                async def run(self, read_stream, write_stream, init_options):
                    try:
                        # Longer wait for cline to prevent race conditions
                        import asyncio
                        await asyncio.sleep(0.5)
                        
                        # Mark as initialized before starting
                        _initialization_complete[session_key] = True
                        logging.info(f"Cline initialization complete for session {session_key}")
                        
                        # Run the actual server
                        await self._server.run(read_stream, write_stream, init_options)
                    except Exception as e:
                        logging.error(f"Cline server run error for {session_key}: {e}")
                        _initialization_complete.pop(session_key, None)
                        raise
            
            aware_server = ClineInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"Cline SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.post("/messages/")
async def handle_get_message(request: Request):
    return await handle_post_message(request)


@mcp_router.post("/{client_name}/sse/{user_id}/messages/")
async def handle_post_message(request: Request):
    return await handle_post_message(request)

async def handle_post_message(request: Request):
    """Handle POST messages for SSE"""
    try:
        body = await request.body()

        # Create a simple receive function that returns the body
        async def receive():
            return {"type": "http.request", "body": body, "more_body": False}

        # Create a simple send function that does nothing
        async def send(message):
            return {}

        # Call handle_post_message with the correct arguments
        await sse.handle_post_message(request.scope, receive, send)

        # Return a success response
        return {"status": "ok"}
    finally:
        pass
        # Clean up context variable
        # client_name_var.reset(client_token)

def setup_mcp_server(app: FastAPI):
    """Setup MCP server with the FastAPI application"""
    mcp._mcp_server.name = f"mem0-mcp-server"

    # Include MCP router in the FastAPI app
    app.include_router(mcp_router)
